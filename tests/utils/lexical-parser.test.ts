/**
 * Tests for the Lexical-Markdown parser system
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { 
  LexicalMarkdownParser,
  LexicalDocument,
  LexicalNode,
  ConverterRegistry,
  initializeLexicalParser
} from '../../src/utils/lexical-parser';

describe('LexicalMarkdownParser', () => {
  beforeEach(() => {
    // Ensure parser is initialized
    initializeLexicalParser();
  });

  describe('Basic Conversion', () => {
    it('should convert simple text to lexical', () => {
      const markdown = 'Hello world';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);
      
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.root.children).toHaveLength(1);
      expect(result.data?.root.children[0].type).toBe('paragraph');
    });

    it('should convert simple lexical to markdown', () => {
      const lexicalDoc: LexicalDocument = {
        root: {
          type: 'root',
          children: [{
            type: 'paragraph',
            children: [{
              type: 'text',
              text: 'Hello world',
              detail: 0,
              format: 0,
              mode: 'normal',
              style: '',
              version: 1
            }],
            direction: 'ltr',
            format: '',
            indent: 0,
            version: 1
          }],
          direction: 'ltr',
          format: '',
          indent: 0,
          version: 1
        }
      };

      const result = LexicalMarkdownParser.lexicalToMarkdown(lexicalDoc);
      
      expect(result.success).toBe(true);
      expect(result.data).toBe('Hello world');
    });

    it('should handle empty content', () => {
      const result = LexicalMarkdownParser.markdownToLexical('');
      
      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(0);
    });
  });

  describe('Headings', () => {
    it('should convert headings correctly', () => {
      const markdown = '# Heading 1\n## Heading 2\n### Heading 3';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);
      
      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(3);
      expect(result.data?.root.children[0].type).toBe('heading');
      expect((result.data?.root.children[0] as any).tag).toBe('h1');
      expect((result.data?.root.children[1] as any).tag).toBe('h2');
      expect((result.data?.root.children[2] as any).tag).toBe('h3');
    });

    it('should convert lexical headings to markdown', () => {
      const lexicalDoc: LexicalDocument = {
        root: {
          type: 'root',
          children: [{
            type: 'heading',
            tag: 'h1',
            children: [{
              type: 'text',
              text: 'Main Title',
              detail: 0,
              format: 0,
              mode: 'normal',
              style: '',
              version: 1
            }],
            direction: 'ltr',
            format: '',
            indent: 0,
            version: 1
          }],
          direction: 'ltr',
          format: '',
          indent: 0,
          version: 1
        }
      };

      const result = LexicalMarkdownParser.lexicalToMarkdown(lexicalDoc);
      
      expect(result.success).toBe(true);
      expect(result.data).toBe('# Main Title');
    });
  });

  describe('Text Formatting', () => {
    it('should handle bold text', () => {
      const markdown = '**bold text**';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);
      
      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.type).toBe('text');
      expect(textNode.format & 1).toBe(1); // Bold flag
    });

    it('should handle italic text', () => {
      const markdown = '*italic text*';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);
      
      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.type).toBe('text');
      expect(textNode.format & 2).toBe(2); // Italic flag
    });

    it('should handle inline code', () => {
      const markdown = '`code text`';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);
      
      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.type).toBe('text');
      expect(textNode.format & 16).toBe(16); // Code flag
    });
  });

  describe('Lists', () => {
    it('should convert unordered lists', () => {
      const markdown = '- Item 1\n- Item 2\n- Item 3';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);
      
      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(1);
      expect(result.data?.root.children[0].type).toBe('list');
      expect((result.data?.root.children[0] as any).listType).toBe('bullet');
    });

    it('should convert ordered lists', () => {
      const markdown = '1. First item\n2. Second item\n3. Third item';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);
      
      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(1);
      expect(result.data?.root.children[0].type).toBe('list');
      expect((result.data?.root.children[0] as any).listType).toBe('number');
    });
  });

  describe('Links', () => {
    it('should convert markdown links', () => {
      const markdown = '[Link text](https://example.com)';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);
      
      expect(result.success).toBe(true);
      const linkNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(linkNode.type).toBe('link');
      expect(linkNode.url).toBe('https://example.com');
    });

    it('should convert lexical links to markdown', () => {
      const lexicalDoc: LexicalDocument = {
        root: {
          type: 'root',
          children: [{
            type: 'paragraph',
            children: [{
              type: 'link',
              url: 'https://example.com',
              children: [{
                type: 'text',
                text: 'Example Link',
                detail: 0,
                format: 0,
                mode: 'normal',
                style: '',
                version: 1
              }],
              direction: 'ltr',
              format: '',
              indent: 0,
              version: 1
            }],
            direction: 'ltr',
            format: '',
            indent: 0,
            version: 1
          }],
          direction: 'ltr',
          format: '',
          indent: 0,
          version: 1
        }
      };

      const result = LexicalMarkdownParser.lexicalToMarkdown(lexicalDoc);
      
      expect(result.success).toBe(true);
      expect(result.data).toBe('[Example Link](https://example.com)');
    });
  });

  describe('Code Blocks', () => {
    it('should convert fenced code blocks', () => {
      const markdown = '```javascript\nconsole.log("Hello");\n```';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);
      
      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(1);
      expect(result.data?.root.children[0].type).toBe('codeblock');
      expect((result.data?.root.children[0] as any).language).toBe('javascript');
    });
  });

  describe('Round-trip Conversion', () => {
    it('should preserve content through round-trip conversion', () => {
      const originalMarkdown = '# Title\n\nThis is **bold** and *italic* text.\n\n- List item 1\n- List item 2\n\n[Link](https://example.com)';
      
      // Markdown → Lexical
      const lexicalResult = LexicalMarkdownParser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);
      
      // Lexical → Markdown
      const markdownResult = LexicalMarkdownParser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);
      
      // The result should be semantically equivalent (may have formatting differences)
      expect(markdownResult.data).toContain('# Title');
      expect(markdownResult.data).toContain('**bold**');
      expect(markdownResult.data).toContain('*italic*');
      expect(markdownResult.data).toContain('- List item 1');
      expect(markdownResult.data).toContain('[Link](https://example.com)');
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid input gracefully', () => {
      const result = LexicalMarkdownParser.markdownToLexical(null as any);
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('should handle invalid lexical document', () => {
      const result = LexicalMarkdownParser.lexicalToMarkdown(null as any);
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('should handle malformed lexical document', () => {
      const malformedDoc = { root: null } as any;
      const result = LexicalMarkdownParser.lexicalToMarkdown(malformedDoc);
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('Ghost-specific Features', () => {
    it('should convert Obsidian callouts to Ghost callouts', () => {
      const markdown = '> [!note]\n> This is a note callout';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);
      
      expect(result.success).toBe(true);
      // The exact structure depends on how callouts are implemented
      // This test may need adjustment based on final implementation
    });
  });
});

describe('ConverterRegistry', () => {
  it('should register and retrieve converters', () => {
    expect(ConverterRegistry.hasConverter('paragraph')).toBe(true);
    expect(ConverterRegistry.hasConverter('heading')).toBe(true);
    expect(ConverterRegistry.hasConverter('text')).toBe(true);
    expect(ConverterRegistry.hasConverter('unknown')).toBe(true); // Fallback
  });

  it('should return registered types', () => {
    const types = ConverterRegistry.getRegisteredTypes();
    expect(types).toContain('paragraph');
    expect(types).toContain('heading');
    expect(types).toContain('text');
  });
});
