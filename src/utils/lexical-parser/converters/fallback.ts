/**
 * Fallback converter for unknown node types
 */

import {
  NodeConverter,
  MarkdownNode,
  LexicalNode,
  ConversionContext,
} from '../types';
import { LexicalUtils } from '../utils/lexical-utils';
import { ContentConverter } from '../../content-converter';

/**
 * <PERSON>les conversion of unknown node types with fallback strategies
 */
export class FallbackConverter extends NodeConverter {
  /**
   * This converter handles all node types as a fallback
   */
  canHandle(nodeType: string): boolean {
    return true;
  }

  /**
   * Convert unknown Markdown node to Lexical format
   */
  markdownToLexical(
    node: MarkdownNode,
    context?: ConversionContext
  ): LexicalNode | LexicalNode[] {
    console.warn(`Unknown markdown node type: ${node.type}, using fallback conversion`);

    // Strategy 1: Convert to markdown card (preserves original formatting)
    if (context?.preserveUnknownNodes) {
      const markdown = this.nodeToMarkdownString(node);
      return {
        type: 'markdown',
        markdown,
        version: 1
      };
    }

    // Strategy 2: Extract text content
    const text = this.extractTextFromNode(node);
    if (text) {
      return LexicalUtils.createTextNode(text);
    }

    // Strategy 3: Return empty text node
    return LexicalUtils.createTextNode('');
  }

  /**
   * Convert unknown Lexical node to Markdown format
   */
  lexicalToMarkdown(
    node: LexicalNode,
    context?: ConversionContext
  ): string {
    console.warn(`Unknown lexical node type: ${node.type}, using fallback conversion`);

    // Strategy 1: Check if it's a markdown card
    if (node.type === 'markdown' && 'markdown' in node) {
      return node.markdown as string;
    }

    // Strategy 2: Use HTML fallback if enabled
    if (context?.fallbackToHTML) {
      try {
        const html = this.lexicalToHTML(node);
        if (html) {
          return ContentConverter.htmlToMarkdown(html);
        }
      } catch (error) {
        console.warn('HTML fallback failed:', error);
      }
    }

    // Strategy 3: Extract text content
    const text = LexicalUtils.extractText(node);
    if (text) {
      return text;
    }

    // Strategy 4: Return comment indicating unknown content
    return `<!-- Unknown Lexical node type: ${node.type} -->`;
  }

  /**
   * Convert a Markdown node to its string representation
   * @private
   */
  private nodeToMarkdownString(node: MarkdownNode): string {
    // Handle different node types with basic markdown formatting
    switch (node.type) {
      case 'text':
        return node.value || '';

      case 'paragraph':
        if (node.children) {
          return node.children.map(child => this.nodeToMarkdownString(child)).join('');
        }
        return '';

      case 'heading':
        const depth = (node as any).depth || 1;
        const prefix = '#'.repeat(Math.min(depth, 6));
        const text = node.children
          ? node.children.map(child => this.nodeToMarkdownString(child)).join('')
          : '';
        return `${prefix} ${text}`;

      case 'strong':
        const strongText = node.children
          ? node.children.map(child => this.nodeToMarkdownString(child)).join('')
          : '';
        return `**${strongText}**`;

      case 'emphasis':
        const emText = node.children
          ? node.children.map(child => this.nodeToMarkdownString(child)).join('')
          : '';
        return `*${emText}*`;

      case 'link':
        const linkText = node.children
          ? node.children.map(child => this.nodeToMarkdownString(child)).join('')
          : '';
        const url = (node as any).url || '';
        return `[${linkText}](${url})`;

      case 'image':
        const alt = (node as any).alt || '';
        const src = (node as any).url || (node as any).src || '';
        return `![${alt}](${src})`;

      case 'code':
        return `\`${node.value || ''}\``;

      case 'codeBlock':
        const lang = (node as any).lang || '';
        const code = node.value || '';
        return `\`\`\`${lang}\n${code}\n\`\`\``;

      case 'list':
        if (node.children) {
          const isOrdered = (node as any).ordered;
          return node.children.map((child, index) => {
            const marker = isOrdered ? `${index + 1}.` : '-';
            const content = this.nodeToMarkdownString(child);
            return `${marker} ${content}`;
          }).join('\n');
        }
        return '';

      case 'listItem':
        if (node.children) {
          return node.children.map(child => this.nodeToMarkdownString(child)).join('');
        }
        return '';

      case 'blockquote':
        if (node.children) {
          const content = node.children.map(child => this.nodeToMarkdownString(child)).join('\n');
          return content.split('\n').map(line => `> ${line}`).join('\n');
        }
        return '';

      default:
        // For unknown types, try to extract any text content
        if (node.value) {
          return node.value;
        }
        if (node.children) {
          return node.children.map(child => this.nodeToMarkdownString(child)).join('');
        }
        return `<!-- ${node.type} -->`;
    }
  }

  /**
   * Extract text content from a node recursively
   * @private
   */
  private extractTextFromNode(node: MarkdownNode): string {
    if (node.value) {
      return node.value;
    }

    if (node.children) {
      return node.children
        .map(child => this.extractTextFromNode(child))
        .join('')
        .trim();
    }

    return '';
  }

  /**
   * Convert Lexical node to HTML (basic implementation)
   * @private
   */
  private lexicalToHTML(node: LexicalNode): string {
    // This is a simplified HTML conversion for fallback purposes
    switch (node.type) {
      case 'text':
        const textNode = node as any;
        let textContent = textNode.text || '';

        // Apply basic formatting
        if (textNode.format) {
          if (textNode.format & 1) textContent = `<strong>${textContent}</strong>`; // Bold
          if (textNode.format & 2) textContent = `<em>${textContent}</em>`; // Italic
          if (textNode.format & 16) textContent = `<code>${textContent}</code>`; // Code
        }

        return textContent;

      case 'paragraph':
        const paragraphNode = node as any;
        if (paragraphNode.children) {
          const content = paragraphNode.children
            .map((child: LexicalNode) => this.lexicalToHTML(child))
            .join('');
          return `<p>${content}</p>`;
        }
        return '<p></p>';

      case 'heading':
        const headingNode = node as any;
        const headingTag = headingNode.tag || 'h1';
        if (headingNode.children) {
          const content = headingNode.children
            .map((child: LexicalNode) => this.lexicalToHTML(child))
            .join('');
          return `<${headingTag}>${content}</${headingTag}>`;
        }
        return `<${headingTag}></${headingTag}>`;

      case 'link':
        const linkNode = node as any;
        const url = linkNode.url || '';
        if (linkNode.children) {
          const content = linkNode.children
            .map((child: LexicalNode) => this.lexicalToHTML(child))
            .join('');
          return `<a href="${url}">${content}</a>`;
        }
        return `<a href="${url}"></a>`;

      case 'list':
        const listNode = node as any;
        const listTag = listNode.listType === 'number' ? 'ol' : 'ul';
        if (listNode.children) {
          const items = listNode.children
            .map((child: LexicalNode) => this.lexicalToHTML(child))
            .join('');
          return `<${listTag}>${items}</${listTag}>`;
        }
        return `<${listTag}></${listTag}>`;

      case 'listitem':
        const listItemNode = node as any;
        if (listItemNode.children) {
          const content = listItemNode.children
            .map((child: LexicalNode) => this.lexicalToHTML(child))
            .join('');
          return `<li>${content}</li>`;
        }
        return '<li></li>';

      default:
        // For unknown types, extract text content
        const extractedText = LexicalUtils.extractText(node);
        return extractedText ? `<span>${extractedText}</span>` : '';
    }
  }

  /**
   * Create a diagnostic comment for unknown nodes
   * @private
   */
  private createDiagnosticComment(node: LexicalNode | MarkdownNode): string {
    const nodeInfo = {
      type: node.type,
      properties: Object.keys(node).filter(key => key !== 'type' && key !== 'children')
    };

    return `<!-- Unknown node: ${JSON.stringify(nodeInfo)} -->`;
  }
}
