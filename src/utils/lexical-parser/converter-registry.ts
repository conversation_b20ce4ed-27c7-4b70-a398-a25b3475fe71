/**
 * Registry for node converters in the Lexical-Markdown parser system
 */

import { NodeConverter } from './types';

/**
 * Registry for managing node converters
 */
export class ConverterRegistry {
  private static converters: Map<string, NodeConverter> = new Map();
  private static fallbackConverter: NodeConverter | null = null;

  /**
   * Register a converter for a specific node type
   * @param nodeType - The node type to handle
   * @param converter - The converter instance
   */
  static register(nodeType: string, converter: NodeConverter): void {
    this.converters.set(nodeType, converter);
  }

  /**
   * Register a fallback converter for unknown node types
   * @param converter - The fallback converter instance
   */
  static registerFallback(converter: NodeConverter): void {
    this.fallbackConverter = converter;
  }

  /**
   * Get a converter for a specific node type
   * @param nodeType - The node type to find a converter for
   * @returns The converter instance or null if not found
   */
  static getConverter(nodeType: string): NodeConverter | null {
    // First try to find a specific converter
    const converter = this.converters.get(nodeType);
    if (converter) {
      return converter;
    }

    // Try to find a converter that can handle this type
    for (const [, conv] of this.converters) {
      if (conv.canHandle(nodeType)) {
        return conv;
      }
    }

    // Return fallback converter if available
    return this.fallbackConverter;
  }

  /**
   * Get all registered converter types
   * @returns Array of registered node types
   */
  static getRegisteredTypes(): string[] {
    return Array.from(this.converters.keys());
  }

  /**
   * Check if a converter is registered for a node type
   * @param nodeType - The node type to check
   * @returns True if a converter is registered
   */
  static hasConverter(nodeType: string): boolean {
    return this.converters.has(nodeType) ||
           Array.from(this.converters.values()).some(conv => conv.canHandle(nodeType)) ||
           this.fallbackConverter !== null;
  }

  /**
   * Unregister a converter for a node type
   * @param nodeType - The node type to unregister
   */
  static unregister(nodeType: string): void {
    this.converters.delete(nodeType);
  }

  /**
   * Clear all registered converters
   */
  static clear(): void {
    this.converters.clear();
    this.fallbackConverter = null;
  }

  /**
   * Get all registered converters
   * @returns Map of all converters
   */
  static getAllConverters(): Map<string, NodeConverter> {
    return new Map(this.converters);
  }

  /**
   * Register default converters
   */
  static registerDefaults(): void {
    // This is now handled by the index.ts file's initializeLexicalParser function
    // to avoid circular dependencies
  }
}
