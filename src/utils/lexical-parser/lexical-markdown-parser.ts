/**
 * Main parser class for bidirectional Obsidian Markdown ↔ Ghost Lexical conversion
 */

import {
  LexicalDocument,
  LexicalNode,
  MarkdownAST,
  MarkdownNode,
  ConversionContext,
  ConversionOptions,
  MarkdownToLexicalResult,
  LexicalToMarkdownResult,
  NodeConverter,
} from './types';
import { ConverterRegistry } from './converter-registry';
import { MarkdownASTParser } from './utils/markdown-ast';
import { LexicalUtils } from './utils/lexical-utils';

/**
 * Main parser class for converting between Markdown and Lexical formats
 */
export class LexicalMarkdownParser {
  private static initialized = false;

  /**
   * Initialize the parser with default converters
   */
  private static initialize(): void {
    if (this.initialized) return;

    // Register default converters (will be implemented in subsequent tasks)
    // ConverterRegistry.registerDefaults();
    
    this.initialized = true;
  }

  /**
   * Convert Markdown to Lexical format
   * @param markdown - The markdown content to convert
   * @param options - Conversion options
   * @returns Lexical document or error result
   */
  static markdownToLexical(
    markdown: string,
    options: ConversionOptions = {}
  ): MarkdownToLexicalResult {
    this.initialize();

    try {
      // Validate input
      if (typeof markdown !== 'string') {
        return {
          success: false,
          error: 'Input must be a string'
        };
      }

      // Handle empty content
      if (!markdown.trim()) {
        return {
          success: true,
          data: this.createEmptyLexicalDocument()
        };
      }

      // Parse markdown to AST
      const ast = MarkdownASTParser.parse(markdown);
      if (!ast) {
        return {
          success: false,
          error: 'Failed to parse markdown'
        };
      }

      // Create conversion context
      const context: ConversionContext = {
        isMarkdownToLexical: true,
        preserveUnknownNodes: options.context?.preserveUnknownNodes ?? true,
        enableGhostFeatures: options.context?.enableGhostFeatures ?? true,
        fallbackToHTML: options.context?.fallbackToHTML ?? true,
      };

      // Convert AST to Lexical
      const lexicalDoc = this.convertASTToLexical(ast, context);

      return {
        success: true,
        data: lexicalDoc
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Convert Lexical format to Markdown
   * @param lexicalDoc - The lexical document to convert
   * @param options - Conversion options
   * @returns Markdown string or error result
   */
  static lexicalToMarkdown(
    lexicalDoc: LexicalDocument,
    options: ConversionOptions = {}
  ): LexicalToMarkdownResult {
    this.initialize();

    try {
      // Validate input
      if (!lexicalDoc || typeof lexicalDoc !== 'object') {
        return {
          success: false,
          error: 'Input must be a valid Lexical document'
        };
      }

      if (!lexicalDoc.root || !Array.isArray(lexicalDoc.root.children)) {
        return {
          success: false,
          error: 'Invalid Lexical document structure'
        };
      }

      // Handle empty document
      if (lexicalDoc.root.children.length === 0) {
        return {
          success: true,
          data: ''
        };
      }

      // Create conversion context
      const context: ConversionContext = {
        isMarkdownToLexical: false,
        preserveUnknownNodes: options.context?.preserveUnknownNodes ?? true,
        enableGhostFeatures: options.context?.enableGhostFeatures ?? true,
        fallbackToHTML: options.context?.fallbackToHTML ?? true,
      };

      // Convert Lexical to Markdown
      const markdown = this.convertLexicalToMarkdown(lexicalDoc, context);

      return {
        success: true,
        data: markdown
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Convert Markdown AST to Lexical document
   * @private
   */
  private static convertASTToLexical(
    ast: MarkdownAST,
    context: ConversionContext
  ): LexicalDocument {
    const children: LexicalNode[] = [];

    for (const node of ast.children) {
      const converted = this.convertMarkdownNodeToLexical(node, context);
      if (Array.isArray(converted)) {
        children.push(...converted);
      } else if (converted) {
        children.push(converted);
      }
    }

    return {
      root: {
        type: 'root',
        children,
        direction: 'ltr',
        format: '',
        indent: 0,
        version: 1
      }
    };
  }

  /**
   * Convert a single Markdown node to Lexical node(s)
   * @private
   */
  private static convertMarkdownNodeToLexical(
    node: MarkdownNode,
    context: ConversionContext
  ): LexicalNode | LexicalNode[] | null {
    // Find appropriate converter
    const converter = ConverterRegistry.getConverter(node.type);
    
    if (converter) {
      try {
        return converter.markdownToLexical(node, context);
      } catch (error) {
        console.warn(`Error converting markdown node ${node.type}:`, error);
      }
    }

    // Fallback handling
    if (context.preserveUnknownNodes) {
      return this.createFallbackLexicalNode(node);
    }

    return null;
  }

  /**
   * Convert Lexical document to Markdown
   * @private
   */
  private static convertLexicalToMarkdown(
    lexicalDoc: LexicalDocument,
    context: ConversionContext
  ): string {
    const markdownParts: string[] = [];

    for (const node of lexicalDoc.root.children) {
      const converted = this.convertLexicalNodeToMarkdown(node, context);
      if (converted) {
        markdownParts.push(converted);
      }
    }

    return markdownParts.join('\n\n').trim();
  }

  /**
   * Convert a single Lexical node to Markdown
   * @private
   */
  private static convertLexicalNodeToMarkdown(
    node: LexicalNode,
    context: ConversionContext
  ): string | null {
    // Find appropriate converter
    const converter = ConverterRegistry.getConverter(node.type);
    
    if (converter) {
      try {
        return converter.lexicalToMarkdown(node, context);
      } catch (error) {
        console.warn(`Error converting lexical node ${node.type}:`, error);
      }
    }

    // Fallback handling
    if (context.preserveUnknownNodes) {
      return this.createFallbackMarkdown(node);
    }

    return null;
  }

  /**
   * Create an empty Lexical document
   * @private
   */
  private static createEmptyLexicalDocument(): LexicalDocument {
    return {
      root: {
        type: 'root',
        children: [],
        direction: 'ltr',
        format: '',
        indent: 0,
        version: 1
      }
    };
  }

  /**
   * Create a fallback Lexical node for unknown Markdown nodes
   * @private
   */
  private static createFallbackLexicalNode(node: MarkdownNode): LexicalNode {
    // Convert unknown node to markdown card
    const markdown = this.nodeToMarkdownString(node);
    return {
      type: 'markdown',
      markdown,
      version: 1
    };
  }

  /**
   * Create fallback Markdown for unknown Lexical nodes
   * @private
   */
  private static createFallbackMarkdown(node: LexicalNode): string {
    // For unknown nodes, try to extract text content or use HTML fallback
    if (node.type === 'markdown' && 'markdown' in node) {
      return node.markdown as string;
    }

    // Extract text from children if available
    if ('children' in node && Array.isArray(node.children)) {
      const textParts: string[] = [];
      for (const child of node.children) {
        if (child.type === 'text' && 'text' in child) {
          textParts.push(child.text as string);
        }
      }
      if (textParts.length > 0) {
        return textParts.join('');
      }
    }

    // Last resort: return a comment indicating unknown content
    return `<!-- Unknown Lexical node type: ${node.type} -->`;
  }

  /**
   * Convert a Markdown node to its string representation
   * @private
   */
  private static nodeToMarkdownString(node: MarkdownNode): string {
    // This is a simplified implementation
    // In practice, this would use a proper markdown serializer
    if (node.type === 'text' && node.value) {
      return node.value;
    }

    if (node.children) {
      return node.children.map(child => this.nodeToMarkdownString(child)).join('');
    }

    return `<!-- ${node.type} -->`;
  }

  /**
   * Register a custom node converter
   */
  static registerConverter(nodeType: string, converter: NodeConverter): void {
    ConverterRegistry.register(nodeType, converter);
  }

  /**
   * Get all registered converters
   */
  static getRegisteredConverters(): string[] {
    return ConverterRegistry.getRegisteredTypes();
  }
}
